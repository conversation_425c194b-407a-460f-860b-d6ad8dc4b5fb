import { Link, useLocation } from "@tanstack/react-router";
import type { LinkProps } from "@tanstack/react-router";
import { useStore } from "@tanstack/react-store";
import {
	Boxes,
	Building2,
	ChevronDown,
	ChevronLeft,
	ChevronRight,
	ChevronUp,
	Factory,
	Package,
	PackageSearch,
	Settings,
	Shield,
	ShoppingCart,
	Store,
	Truck,
	Users,
	Warehouse,
} from "lucide-react";
import { useEffect, useState } from "react";
import { sidebarActions, sidebarIsCollapsed } from "../store/sidebar";

type MenuProps = Pick<LinkProps, "to"> & {
	name: string;
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	icon: any;
	items?: MenuProps[];
};

type MenuGroup = {
	name: string;
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	icon: any;
	items: MenuProps[];
};

const menuGroups = [
	{
		name: "Datos maestros",
		icon: Package,
		items: [
			{ name: "categorias", icon: Boxes, to: "/admin/products/categories" },
			{ name: "marcas", icon: Store, to: "/admin/products/brands" },
			{
				name: "unidades de medida",
				icon: Store,
				to: "/admin/products/measurement-units",
			},
			{
				name: "productos comerciales",
				icon: PackageSearch,
				to: "/admin/products/products",
			},
			{ name: "insumos", icon: Truck, to: "/admin/products/suppliers" },
			{
				name: "materiales",
				icon: Warehouse,
				to: "/admin/products/materials",
			},
			{
				name: "materias primas",
				icon: Boxes,
				to: "/admin/products/raw-materials",
			},
			{
				name: "equipos de producción",
				icon: Warehouse,
				to: "/admin/products/production-devices",
			},
		],
	},
	{
		name: "Produccion",
		icon: Factory,
		items: [
			{ name: "recetas", icon: Package, to: "/admin/manufacture/recipes" },
			{
				name: "flujo de producción",
				icon: Building2,
				to: "/admin/manufacture/production-flow",
			},
			{
				name: "areas de trabajo",
				icon: Warehouse,
				to: "/admin/manufacture/work-area",
			},
			{
				name: "operaciones",
				icon: ShoppingCart,
				to: "/admin/manufacture/operations",
			},
			{
				name: "simulación de producción",
				icon: Package,
				to: "/admin/manufacture/recipe-test",
			},
			{
				name: "solicitudes de producción",
				icon: Package,
				to: "/admin/manufacture/production-requests",
			},
		],
	},
	{
		name: "ventas",
		icon: ShoppingCart,
		items: [{ name: "ventas", icon: ShoppingCart, to: "/admin/sales" }],
	},
	{
		name: "clientes",
		icon: Users,
		items: [{ name: "clientes", icon: Users, to: "/admin/clients/clients" }],
	},
	{
		name: "inventarios",
		icon: Warehouse,
		items: [
			{
				name: "inventario",
				icon: Warehouse,
				to: "/admin/inventory/warehouses/inventory",
			},
			{ name: "Almacenes", icon: Boxes, to: "/admin/inventory/warehouses" },
		],
	},
	{
		name: "seguridad",
		icon: Shield,
		items: [{ name: "usuarios", icon: Users, to: "/admin/security/users" }],
	},
	{
		name: "configuración",
		icon: Settings,
		items: [
			{
				name: "giros de negocio",
				icon: Building2,
				to: "/admin/settings/business-lines",
			},
			{
				name: "Canales de ventas",
				icon: ShoppingCart,
				to: "/admin/settings/channels",
			},
			{
				name: "Zonas",
				icon: Truck,
				to: "/admin/settings/areas",
			},
			{
				name: "Vendedores",
				icon: Users,
				to: "/admin/settings/sellers",
			},
		],
	},
] as const satisfies MenuProps[];

export default function Sidebar() {
	const isCollapsed = useStore(sidebarIsCollapsed);
	const location = useLocation();
	const [openGroup, setOpenGroup] = useState<string | null>(null);

	// Find which group contains the current route and open it
	useEffect(() => {
		const currentPath = location.pathname;
		const activeGroup = menuGroups.find((group) =>
			group.items?.some((item: MenuProps) =>
				currentPath.startsWith(item.to || ""),
			),
		);
		if (activeGroup) {
			setOpenGroup(activeGroup.name);
		}
	}, [location.pathname]);

	const toggleGroup = (groupName: string) => {
		setOpenGroup((prev) => (prev === groupName ? null : groupName));
	};

	// Helper function to check if a group is active (contains current route)
	const isGroupActive = (group: MenuProps) => {
		return (
			group.items?.some((item: MenuProps) =>
				location.pathname.startsWith(item.to || ""),
			) || false
		);
	};

	return (
		<div
			className={`h-full flex-shrink-0 bg-base-300 shadow-lg transition-all duration-300 ${
				isCollapsed ? "w-16" : "w-64"
			}`}
		>
			<div className="flex h-full flex-col">
				<div className="flex items-center justify-between p-4">
					{!isCollapsed && (
						<h2 className="font-bold text-2xl text-primary">Fhyona</h2>
					)}
					<button
						type="button"
						onClick={sidebarActions.toggleCollapsed}
						className="btn btn-ghost btn-sm"
					>
						{isCollapsed ? (
							<ChevronRight className="h-5 w-5" />
						) : (
							<ChevronLeft className="h-5 w-5" />
						)}
					</button>
				</div>

				<div className="flex-1 overflow-y-auto p-2">
					{menuGroups.map((group) => {
						const isActive = isGroupActive(group);
						const isOpen = openGroup === group.name;

						return (
							<div key={group.name} className="mb-2">
								<button
									type="button"
									onClick={() => toggleGroup(group.name)}
									className={`flex w-full items-center gap-3 rounded-lg px-3 py-2 text-left transition-colors ${
										isActive
											? "bg-primary font-semibold text-primary-content"
											: "hover:bg-base-200"
									}`}
								>
									<group.icon className="h-5 w-5" />
									{!isCollapsed && (
										<>
											<span className="flex-1 capitalize">{group.name}</span>
											{group.items &&
												group.items.length > 0 &&
												(isOpen ? (
													<ChevronUp className="h-4 w-4" />
												) : (
													<ChevronDown className="h-4 w-4" />
												))}
										</>
									)}
								</button>

								{!isCollapsed && isOpen && group.items && (
									<div className="mt-1 space-y-1 pl-8">
										{group.items.map((item: MenuProps) => (
											<Link
												key={item.name}
												to={item.to}
												className="flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors hover:bg-base-200"
												activeProps={{
													className: "font-bold bg-base-200",
												}}
											>
												<item.icon className="h-4 w-4" />
												<span className="capitalize">{item.name}</span>
											</Link>
										))}
									</div>
								)}
							</div>
						);
					})}
				</div>
			</div>
		</div>
	);
}
