import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Client, CreateClient } from "../service/model/client";
import { clientOptions } from "./client-options";

export default function useCreateClient() {
	const service = useService();
	const { client } = service;
	const queryClient = useQueryClient();
	const queryKey = clientOptions(service).queryKey;

	return useMutation({
		mutationKey: ["create-client"],
		mutationFn: (createClient: CreateClient) =>
			AppRuntime.runPromise(client.create(createClient)),
		onMutate: async (createClient) => {
			await queryClient.cancelQueries({ queryKey });

			const previousClients = queryClient.getQueryData(queryKey);

			if (previousClients) {
				const newClient: Client = {
					id: "temp-id",
					name: createClient.name,
					fatherName: createClient.fatherName || null,
					motherName: createClient.motherName || null,
					clientType: createClient.clientType,
					documentType: createClient.documentType,
					document: createClient.document,
					ubication: createClient.ubication || null,
					socialReason: createClient.socialReason || null,
					commercialName: createClient.commercialName || null,
					condition: createClient.condition || null,
					state: createClient.state || null,
					hasRetentionRegime: createClient.hasRetentionRegime || null,
					businessLineID: createClient.businessLineID || null,
					subBusinessLineID: createClient.subBusinessLineID || null,
					channelID: createClient.channelID || null,
					sellerID: createClient.sellerID || null,
					contactName: createClient.contactName || null,
					email: createClient.email || null,
					phone: createClient.phone || null,
					createdAt: new Date().toISOString(),
					updatedAt: null,
					deletedAt: null,
				};

				queryClient.setQueryData(
					queryKey,
					create(previousClients, (draft) => {
						draft.push(newClient);
					}),
				);
			}

			return { previousClients };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousClients);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
