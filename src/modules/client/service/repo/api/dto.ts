import { Schema } from "effect";
import { Client, CreateClient, UpdateClient } from "../../model/client";

export const SellerResultApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	father_name: Schema.String,
	mother_name: Schema.String,
	identity_document_number: Schema.String,
	state: Schema.String,
});

export const ClientApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	father_name: Schema.NullOr(Schema.String),
	mother_name: Schema.NullOr(Schema.String),
	client_type: Schema.String,
	document_type: Schema.String,
	document: Schema.String,
	ubication: Schema.NullOr(Schema.String),
	social_reason: Schema.NullOr(Schema.String),
	commercial_name: Schema.NullOr(Schema.String),
	condition: Schema.NullOr(Schema.String),
	state: Schema.NullOr(Schema.String),
	has_retention_regime: Schema.NullOr(Schema.Boolean),
	business_line_id: Schema.NullOr(Schema.String),
	sub_business_line_id: Schema.NullOr(Schema.String),
	channel_id: Schema.NullOr(Schema.String),
	seller_id: Schema.NullOr(Schema.String),
	seller: Schema.NullOr(SellerResultApi),
	contact_name: Schema.NullOr(Schema.String),
	email: Schema.NullOr(Schema.String),
	phone: Schema.NullOr(Schema.String),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const ClientFromApi = Schema.transform(ClientApi, Client, {
	strict: true,
	decode: (clientApi) => ({
		id: clientApi.id,
		name: clientApi.name,
		fatherName: clientApi.father_name,
		motherName: clientApi.mother_name,
		clientType: clientApi.client_type,
		documentType: clientApi.document_type,
		document: clientApi.document,
		ubication: clientApi.ubication,
		socialReason: clientApi.social_reason,
		commercialName: clientApi.commercial_name,
		condition: clientApi.condition,
		state: clientApi.state,
		hasRetentionRegime: clientApi.has_retention_regime,
		businessLineID: clientApi.business_line_id,
		subBusinessLineID: clientApi.sub_business_line_id,
		channelID: clientApi.channel_id,
		sellerID: clientApi.seller_id,
		seller: clientApi.seller
			? {
					id: clientApi.seller.id,
					name: clientApi.seller.name,
					fatherName: clientApi.seller.father_name,
					motherName: clientApi.seller.mother_name,
					identityDocumentNumber: clientApi.seller.identity_document_number,
					state: clientApi.seller.state,
				}
			: null,
		contactName: clientApi.contact_name,
		email: clientApi.email,
		phone: clientApi.phone,
		createdAt: clientApi.created_at,
		updatedAt: clientApi.updated_at,
		deletedAt: clientApi.deleted_at,
	}),
	encode: (client) => ({
		id: client.id,
		name: client.name,
		father_name: client.fatherName,
		mother_name: client.motherName,
		client_type: client.clientType,
		document_type: client.documentType,
		document: client.document,
		ubication: client.ubication,
		social_reason: client.socialReason,
		commercial_name: client.commercialName,
		condition: client.condition,
		state: client.state,
		has_retention_regime: client.hasRetentionRegime,
		business_line_id: client.businessLineID,
		sub_business_line_id: client.subBusinessLineID,
		channel_id: client.channelID,
		seller_id: client.sellerID,
		seller: client.seller
			? {
					id: client.seller.id,
					name: client.seller.name,
					father_name: client.seller.fatherName,
					mother_name: client.seller.motherName,
					identity_document_number: client.seller.identityDocumentNumber,
					state: client.seller.state,
				}
			: null,
		contact_name: client.contactName,
		email: client.email,
		phone: client.phone,
		created_at: client.createdAt,
		updated_at: client.updatedAt,
		deleted_at: client.deletedAt,
	}),
});

export const ClientListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(ClientFromApi))),
	Schema.mutable(Schema.Array(Client)),
	{
		strict: true,
		decode: (clientApiList) => (clientApiList ? clientApiList : []),
		encode: (clientList) => clientList,
	},
);

export const CreateClientApi = Schema.Struct({
	name: Schema.String,
	father_name: Schema.optional(Schema.String),
	mother_name: Schema.optional(Schema.String),
	client_type: Schema.String,
	document_type: Schema.String,
	document: Schema.String,
	ubication: Schema.optional(Schema.String),
	social_reason: Schema.optional(Schema.String),
	commercial_name: Schema.optional(Schema.String),
	condition: Schema.optional(Schema.String),
	state: Schema.optional(Schema.String),
	has_retention_regime: Schema.optional(Schema.Boolean),
	business_line_id: Schema.optional(Schema.String),
	sub_business_line_id: Schema.optional(Schema.String),
	channel_id: Schema.optional(Schema.String),
	seller_id: Schema.optional(Schema.String),
	contact_name: Schema.optional(Schema.String),
	email: Schema.optional(Schema.String),
	phone: Schema.optional(Schema.String),
});

export const CreateClientApiFromCreateClient = Schema.transform(
	CreateClient,
	CreateClientApi,
	{
		strict: true,
		decode: (createClient) => ({
			name: createClient.name,
			father_name: createClient.fatherName,
			mother_name: createClient.motherName,
			client_type: createClient.clientType,
			document_type: createClient.documentType,
			document: createClient.document,
			ubication: createClient.ubication,
			social_reason: createClient.socialReason,
			commercial_name: createClient.commercialName,
			condition: createClient.condition,
			state: createClient.state,
			has_retention_regime: createClient.hasRetentionRegime,
			business_line_id: createClient.businessLineID,
			sub_business_line_id: createClient.subBusinessLineID,
			channel_id: createClient.channelID,
			seller_id: createClient.sellerID,
			contact_name: createClient.contactName,
			email: createClient.email,
			phone: createClient.phone,
		}),
		encode: (createClientApi) => ({
			name: createClientApi.name,
			fatherName: createClientApi.father_name,
			motherName: createClientApi.mother_name,
			clientType: createClientApi.client_type,
			documentType: createClientApi.document_type,
			document: createClientApi.document,
			ubication: createClientApi.ubication,
			socialReason: createClientApi.social_reason,
			commercialName: createClientApi.commercial_name,
			condition: createClientApi.condition,
			state: createClientApi.state,
			hasRetentionRegime: createClientApi.has_retention_regime,
			businessLineID: createClientApi.business_line_id,
			subBusinessLineID: createClientApi.sub_business_line_id,
			channelID: createClientApi.channel_id,
			sellerID: createClientApi.seller_id,
			contactName: createClientApi.contact_name,
			email: createClientApi.email,
			phone: createClientApi.phone,
		}),
	},
);

export const UpdateClientApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	father_name: Schema.optional(Schema.String),
	mother_name: Schema.optional(Schema.String),
	client_type: Schema.String,
	document_type: Schema.String,
	document: Schema.String,
	ubication: Schema.optional(Schema.String),
	social_reason: Schema.optional(Schema.String),
	commercial_name: Schema.optional(Schema.String),
	condition: Schema.optional(Schema.String),
	state: Schema.optional(Schema.String),
	has_retention_regime: Schema.optional(Schema.Boolean),
	business_line_id: Schema.optional(Schema.String),
	sub_business_line_id: Schema.optional(Schema.String),
	channel_id: Schema.optional(Schema.String),
	seller_id: Schema.optional(Schema.String),
	contact_name: Schema.optional(Schema.String),
	email: Schema.optional(Schema.String),
	phone: Schema.optional(Schema.String),
});

export const UpdateClientApiFromUpdateClient = Schema.transform(
	UpdateClient,
	UpdateClientApi,
	{
		strict: true,
		decode: (updateClient) => ({
			id: updateClient.id,
			name: updateClient.name,
			father_name: updateClient.fatherName,
			mother_name: updateClient.motherName,
			client_type: updateClient.clientType,
			document_type: updateClient.documentType,
			document: updateClient.document,
			ubication: updateClient.ubication,
			social_reason: updateClient.socialReason,
			commercial_name: updateClient.commercialName,
			condition: updateClient.condition,
			state: updateClient.state,
			has_retention_regime: updateClient.hasRetentionRegime,
			business_line_id: updateClient.businessLineID,
			sub_business_line_id: updateClient.subBusinessLineID,
			channel_id: updateClient.channelID,
			seller_id: updateClient.sellerID,
			contact_name: updateClient.contactName,
			email: updateClient.email,
			phone: updateClient.phone,
		}),
		encode: (updateClientApi) => ({
			id: updateClientApi.id,
			name: updateClientApi.name,
			fatherName: updateClientApi.father_name,
			motherName: updateClientApi.mother_name,
			clientType: updateClientApi.client_type,
			documentType: updateClientApi.document_type,
			document: updateClientApi.document,
			ubication: updateClientApi.ubication,
			socialReason: updateClientApi.social_reason,
			commercialName: updateClientApi.commercial_name,
			condition: updateClientApi.condition,
			state: updateClientApi.state,
			hasRetentionRegime: updateClientApi.has_retention_regime,
			businessLineID: updateClientApi.business_line_id,
			subBusinessLineID: updateClientApi.sub_business_line_id,
			channelID: updateClientApi.channel_id,
			sellerID: updateClientApi.seller_id,
			contactName: updateClientApi.contact_name,
			email: updateClientApi.email,
			phone: updateClientApi.phone,
		}),
	},
);

export const CreateClientApiResponse = Schema.String;
