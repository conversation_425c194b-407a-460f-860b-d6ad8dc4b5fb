import { useQuery } from "@tanstack/react-query";
import {
	Building,
	FileText,
	IdCard,
	Mail,
	MapPin,
	Phone,
	Tag,
	User,
	Users,
} from "lucide-react";
import { useEffect, useRef } from "react";
import { useService } from "src/config/context/serviceProvider";
import { useAppForm } from "src/core/components/form/form";
import {
	businessLineOptions,
	businessLineParentsOptions,
} from "src/modules/business-line/hooks/business-line-options";
import { channelOptions } from "src/modules/channel/hooks/channel-options";
import { sellerOptions } from "src/modules/seller/hooks/seller-options";
import type { CreateClient, UpdateClient } from "../../service/model/client";
import { type ClientFormData, clientFormSchema } from "./schema";

interface ClientFormProps {
	onSubmit: (data: CreateClient | UpdateClient) => void;
	defaultValues?: Partial<ClientFormData & { id?: string }>;
	isLoading?: boolean;
	submitText?: string;
	isEdit?: boolean;
}

export default function ClientForm({
	onSubmit,
	defaultValues,
	isLoading = false,
	submitText = "Guardar",
	isEdit = false,
}: ClientFormProps) {
	const service = useService();

	const { data: parentBusinessLines = [] } = useQuery(
		businessLineParentsOptions(service),
	);
	const { data: allBusinessLines = [] } = useQuery(
		businessLineOptions(service),
	);
	const { data: channels = [] } = useQuery(channelOptions(service));
	const { data: sellers = [] } = useQuery(sellerOptions(service));

	const form = useAppForm({
		defaultValues:
			defaultValues ||
			({
				name: "",
				fatherName: "",
				motherName: "",
				clientType: "",
				documentType: "",
				document: "",
				ubication: "",
				socialReason: "",
				commercialName: "",
				condition: "",
				state: "",
				hasRetentionRegime: false,
				businessLineId: "",
				subBusinessLineId: "",
				channelId: "",
				sellerId: "",
				contactName: "",
				email: "",
				phone: "",
			} as ClientFormData),
		validators: {
			onChange: clientFormSchema,
		},
		onSubmit: ({ value }) => {
			const clientData = {
				...value,
				businessLineID: value.businessLineId || undefined,
				subBusinessLineID: value.subBusinessLineId || undefined,
				channelID: value.channelId || undefined,
				sellerID: value.sellerId || undefined,
			};

			// Remove form-specific fields
			const {
				businessLineId,
				subBusinessLineId,
				channelId,
				sellerId,
				...submitData
			} = clientData;

			// Create the proper payload based on whether it's an edit or create
			if (isEdit && defaultValues?.id) {
				const updateData: UpdateClient = {
					...submitData,
					id: defaultValues.id,
				} as UpdateClient;
				onSubmit(updateData);
			} else {
				onSubmit(submitData as CreateClient);
			}
		},
	});

	// Flag to prevent infinite loops when programmatically updating fields
	const isUpdatingProgrammatically = useRef(false);

	// Clear subBusinessLineId when businessLineId changes
	useEffect(() => {
		const subscription = form.store.subscribe(() => {
			const state = form.store.state;
			const businessLineId = state.values.businessLineId;
			const subBusinessLineId = state.values.subBusinessLineId;

			if (
				subBusinessLineId &&
				businessLineId &&
				!isUpdatingProgrammatically.current
			) {
				// Check if current subBusinessLineId is valid for the selected businessLineId
				const availableSubLines = allBusinessLines.filter(
					(bl) => bl.parentId === businessLineId,
				);

				if (!availableSubLines.some((bl) => bl.id === subBusinessLineId)) {
					isUpdatingProgrammatically.current = true;
					form.setFieldValue("subBusinessLineId", "");
					// Reset flag after a short delay
					setTimeout(() => {
						isUpdatingProgrammatically.current = false;
					}, 10);
				}
			}
		});

		return subscription;
	}, [form, allBusinessLines]);

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}
			className="space-y-6"
		>
			<form.AppForm>
				<div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
					{/* Left Column - Basic Information */}
					<div className="space-y-6">
						<h3 className="border-b pb-2 font-semibold text-base-content text-lg">
							Información Básica
						</h3>
						<div className="space-y-4">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre *"
										placeholder="Ingrese el nombre"
										prefixComponent={<User size={16} />}
									/>
								)}
							/>

							<form.AppField
								name="clientType"
								children={({ FSButtonGroupField }) => (
									<FSButtonGroupField
										label="Tipo de Cliente *"
										details="Seleccione el tipo de cliente según su naturaleza jurídica"
										options={[
											{
												value: "natural",
												label: "Natural",
												icon: <User size={20} />,
												description: "Persona física",
											},
											{
												value: "juridica",
												label: "Jurídica",
												icon: <Building size={20} />,
												description: "Empresa o entidad",
											},
										]}
									/>
								)}
							/>

							<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
								<form.AppField
									name="documentType"
									children={({ FSSelectField }) => (
										<FSSelectField
											label="Tipo de Documento *"
											placeholder="Seleccione tipo"
											options={[
												{ value: "dni", label: "DNI" },
												{ value: "ruc", label: "RUC" },
											]}
										/>
									)}
								/>
							</div>

							<form.AppField
								name="document"
								children={({ FSTextField }) => (
									<FSTextField
										label="Documento *"
										placeholder="Ingrese el documento"
										prefixComponent={<IdCard size={16} />}
									/>
								)}
							/>

							<form.AppField
								name="ubication"
								children={({ FSTextField }) => (
									<FSTextField
										label="Dirección"
										placeholder="Ingrese dirección"
										prefixComponent={<MapPin size={16} />}
									/>
								)}
							/>

							{/* Conditional fields for natural clients */}
							<form.Subscribe
								selector={(state) => state.values.clientType}
								children={(clientType) => (
									<>
										{clientType === "natural" && (
											<div className="space-y-4">
												<form.AppField
													name="fatherName"
													children={({ FSTextField }) => (
														<FSTextField
															label="Apellido Paterno"
															placeholder="Ingrese apellido paterno"
															prefixComponent={<Users size={16} />}
														/>
													)}
												/>

												<form.AppField
													name="motherName"
													children={({ FSTextField }) => (
														<FSTextField
															label="Apellido Materno"
															placeholder="Ingrese apellido materno"
															prefixComponent={<Users size={16} />}
														/>
													)}
												/>
											</div>
										)}

										{clientType === "juridica" && (
											<div className="space-y-4">
												<form.AppField
													name="socialReason"
													children={({ FSTextField }) => (
														<FSTextField
															label="Razón Social"
															placeholder="Ingrese razón social"
															prefixComponent={<FileText size={16} />}
														/>
													)}
												/>

												<form.AppField
													name="commercialName"
													children={({ FSTextField }) => (
														<FSTextField
															label="Nombre Comercial"
															placeholder="Ingrese nombre comercial"
															prefixComponent={<Tag size={16} />}
														/>
													)}
												/>

												<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
													<form.AppField
														name="condition"
														children={({ FSTextField }) => (
															<FSTextField
																label="Condición"
																placeholder="Ingrese condición"
																prefixComponent={<FileText size={16} />}
															/>
														)}
													/>

													<form.AppField
														name="state"
														children={({ FSTextField }) => (
															<FSTextField
																label="Estado"
																placeholder="Ingrese estado"
																prefixComponent={<FileText size={16} />}
															/>
														)}
													/>
												</div>

												<form.AppField
													name="hasRetentionRegime"
													children={({ FSToggleField }) => (
														<FSToggleField label="Régimen de Retención" />
													)}
												/>
											</div>
										)}
									</>
								)}
							/>
						</div>
					</div>

					{/* Right Column - Business Information */}
					<div className="space-y-6">
						<h3 className="border-b pb-2 font-semibold text-base-content text-lg">
							Información Comercial
						</h3>
						<div className="space-y-4">
							<form.AppField
								name="businessLineId"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Giro de Negocio"
										placeholder="Seleccione giro principal"
										options={parentBusinessLines.map((bl) => ({
											value: bl.id,
											label: bl.name,
										}))}
									/>
								)}
							/>

							<form.Subscribe
								selector={(state) => state.values.businessLineId}
								children={(businessLineId) => {
									// Get filtered business lines based on parent selection
									const availableSubLines = businessLineId
										? allBusinessLines.filter(
												(bl) => bl.parentId === businessLineId,
											)
										: allBusinessLines.filter((bl) => bl.parentId !== null);

									return (
										<form.AppField
											name="subBusinessLineId"
											children={(field) => {
												const isTouched = field.state.meta.isTouched;
												const errorsLength = field.state.meta.errors.length;
												const isError = isTouched && errorsLength;
												const isValid =
													isTouched &&
													!field.state.meta.isValidating &&
													!!field.state.value;
												const errors = field.state.meta.errors;

												return (
													<fieldset className="fieldset">
														<legend className="fieldset-legend">
															Sub Giro de Negocio
														</legend>
														<select
															className={`select w-full ${isError ? "select-error" : isValid ? "select-success" : ""}`}
															value={field.state.value?.toString() || ""}
															onChange={(e) => {
																const selectedValue = e.target.value;
																field.handleChange(selectedValue);

																// Only automatically set the main business line when:
																// 1. A sub business line is actually selected (not empty)
																// 2. We're not in the middle of a programmatic update
																if (
																	selectedValue &&
																	selectedValue.trim() !== "" &&
																	!isUpdatingProgrammatically.current
																) {
																	const selectedSubLine = allBusinessLines.find(
																		(bl) => bl.id === selectedValue,
																	);
																	if (selectedSubLine?.parentId) {
																		isUpdatingProgrammatically.current = true;
																		form.setFieldValue(
																			"businessLineId",
																			selectedSubLine.parentId,
																		);
																		// Reset flag after a short delay
																		setTimeout(() => {
																			isUpdatingProgrammatically.current = false;
																		}, 10);
																	}
																}
															}}
														>
															<option disabled value="">
																Seleccione Sub giro de negocio
															</option>
															{availableSubLines.map((bl) => (
																<option key={bl.id} value={bl.id}>
																	{bl.name}
																</option>
															))}
														</select>
														{isError
															? errors.map((error, index) => {
																	const errorMessage =
																		typeof error === "string"
																			? error
																			: error?.message || "Error de validación";
																	return (
																		<p
																			key={`error-${index}-${errorMessage}`}
																			className="fieldset-label text-error"
																		>
																			{errorMessage}
																		</p>
																	);
																})
															: null}
													</fieldset>
												);
											}}
										/>
									);
								}}
							/>

							<form.AppField
								name="channelId"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Canal"
										placeholder="Seleccione canal"
										options={channels.map((channel) => ({
											value: channel.id,
											label: channel.name,
										}))}
									/>
								)}
							/>

							<form.AppField
								name="sellerId"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Vendedor"
										placeholder="Seleccione vendedor"
										options={sellers.map((seller) => ({
											value: seller.id,
											label: seller.name,
										}))}
									/>
								)}
							/>

							<form.AppField
								name="contactName"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre de Contacto"
										placeholder="Ingrese nombre de contacto"
										prefixComponent={<User size={16} />}
									/>
								)}
							/>

							<form.AppField
								name="email"
								children={({ FSTextField }) => (
									<FSTextField
										label="Email"
										placeholder="Ingrese email"
										type="email"
										prefixComponent={<Mail size={16} />}
									/>
								)}
							/>

							<form.AppField
								name="phone"
								children={({ FSTextField }) => (
									<FSTextField
										label="Teléfono"
										placeholder="Ingrese teléfono"
										prefixComponent={<Phone size={16} />}
									/>
								)}
							/>
						</div>
					</div>
				</div>

				<div className="flex justify-end gap-4 border-t pt-6">
					<button
						type="submit"
						disabled={isLoading}
						className="btn btn-primary"
					>
						{isLoading && (
							<span className="loading loading-spinner loading-sm" />
						)}
						{submitText}
					</button>
				</div>
			</form.AppForm>
		</form>
	);
}
