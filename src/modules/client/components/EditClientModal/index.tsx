import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import { clientOptionsById } from "../../hooks/client-options";
import useUpdateClient from "../../hooks/use-update-client";
import type { UpdateClient } from "../../service/model/client";
import ClientForm from "../ClientForm";

interface EditClientModalProps {
	isOpen: boolean;
	setIsOpen: (isOpen: boolean) => void;
	id: string;
}

export default function EditClientModal({
	isOpen,
	setIsOpen,
	id,
}: EditClientModalProps) {
	const service = useService();
	const updateClientMutation = useUpdateClient();

	const { data: client, isLoading } = useQuery({
		...clientOptionsById(service, id),
		enabled: isOpen && !!id,
	});

	const handleSubmit = (data: UpdateClient) => {
		updateClientMutation.mutate(
			{ ...data, id },
			{
				onSuccess: () => {
					setIsOpen(false);
				},
			},
		);
	};

	useEffect(() => {
		const modal = document.getElementById(
			"edit-client-modal",
		) as HTMLDialogElement;
		if (isOpen) {
			modal?.showModal();
		} else {
			modal?.close();
		}
	}, [isOpen]);

	const defaultValues = client
		? {
				name: client.name,
				fatherName: client.fatherName || "",
				motherName: client.motherName || "",
				clientType: client.clientType,
				documentType: client.documentType,
				document: client.document,
				ubication: client.ubication || "",
				socialReason: client.socialReason || "",
				commercialName: client.commercialName || "",
				condition: client.condition || "",
				state: client.state || "",
				hasRetentionRegime: client.hasRetentionRegime || false,
				businessLineId: client.businessLineID || "",
				subBusinessLineId: client.subBusinessLineID || "",
				channelId: client.channelID || "",
				sellerId: client.sellerID || "",
				contactName: client.contactName || "",
				email: client.email || "",
				phone: client.phone || "",
			}
		: undefined;

	return (
		<dialog id="edit-client-modal" className="modal">
			<div className="modal-box max-w-4xl">
				<h3 className="mb-4 font-bold text-lg">Editar Cliente</h3>
				{isLoading ? (
					<div className="flex justify-center py-8">
						<span className="loading loading-spinner loading-lg" />
					</div>
				) : (
					<ClientForm
						onSubmit={handleSubmit}
						defaultValues={defaultValues}
						isLoading={updateClientMutation.isPending}
						submitText="Actualizar"
					/>
				)}
				<div className="modal-action">
					<button
						type="button"
						className="btn"
						onClick={() => setIsOpen(false)}
						disabled={updateClientMutation.isPending}
					>
						Cancelar
					</button>
				</div>
			</div>
			<form method="dialog" className="modal-backdrop">
				<button type="button" onClick={() => setIsOpen(false)}>
					close
				</button>
			</form>
		</dialog>
	);
}
