import { Effect } from "effect";
import type { AppError } from "src/core/service/model/error";
import type { CreateProductionRequest, ProductionRequest, UpdateProductionRequest } from "./production-request";

export class ProductionRequestUsecase extends Effect.Tag("ProductionRequestUsecase")<
	ProductionRequestUsecase,
	{
		readonly getAll: () => Effect.Effect<ProductionRequest[], AppError>;
		readonly getById: (id: string) => Effect.Effect<ProductionRequest, AppError>;
		readonly create: (productionRequest: CreateProductionRequest) => Effect.Effect<string, AppError>;
		readonly update: (productionRequest: UpdateProductionRequest) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
		readonly approve: (id: string) => Effect.Effect<void, AppError>;
		readonly reject: (id: string) => Effect.Effect<void, AppError>;
	}
>() {}
