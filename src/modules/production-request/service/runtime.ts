import { Layer, ManagedRuntime } from "effect";
import { productionRequestApiRepoLive } from "./repo/api/production-request-api";
import { ProductionRequestRepositoryLive } from "./repository";
import { productionRequestUsecaseLive } from "./usecase";

const makeProductionRequestUsecaseLive = productionRequestUsecaseLive.pipe(
	Layer.provide(ProductionRequestRepositoryLive),
	Layer.provide(productionRequestApiRepoLive),
);

const ProductionRequestLayer = makeProductionRequestUsecaseLive;

export const ProductionRequestRuntime = ManagedRuntime.make(ProductionRequestLayer);
