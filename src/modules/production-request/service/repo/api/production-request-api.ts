import { HttpBody } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "src/core/service/repo/api";
import {
	handleDResponse,
	handleResponse,
} from "src/core/service/repo/api/utils";
import type {
	CreateProductionRequest,
	UpdateProductionRequest,
} from "../../model/production-request";
import { ProductionRequestRepository } from "../../model/repository";
import {
	CreateProductionRequestApiFromCreateProductionRequest,
	CreateProductionRequestApiResponse,
	ProductionRequestFromApi,
	ProductionRequestListFromApi,
	UpdateProductionRequestApiFromUpdateProductionRequest,
} from "./dto";

const baseUrl = "/v1/production-requests";

const makeProductionRequestApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(ProductionRequestListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(ProductionRequestFromApi))),
		create: (productionRequest: CreateProductionRequest) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(
							CreateProductionRequestApiFromCreateProductionRequest,
						)(productionRequest),
					),
				})
				.pipe(
					Effect.flatMap(handleDResponse(CreateProductionRequestApiResponse)),
				),
		update: (productionRequest: UpdateProductionRequest) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(
							UpdateProductionRequestApiFromUpdateProductionRequest,
						)(productionRequest),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		validateCode: (code: string) =>
			httpClient
				.get(`${baseUrl}/validate/code/${code}`)
				.pipe(Effect.flatMap(handleResponse)),
		approve: (id: string) =>
			httpClient
				.patch(`${baseUrl}/${id}/approve`)
				.pipe(Effect.flatMap(handleResponse)),
		reject: (id: string) =>
			httpClient
				.patch(`${baseUrl}/${id}/reject`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const productionRequestApiRepoLive = Layer.effect(
	ProductionRequestRepository,
	makeProductionRequestApiRepo,
);
