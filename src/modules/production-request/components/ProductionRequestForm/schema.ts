import * as v from "valibot";

export const ProductionRequestItemSchema = v.object({
	productId: v.pipe(v.string(), v.minLength(1, "Producto es requerido")),
	quantity: v.pipe(
		v.number(),
		v.minValue(1, "La cantidad debe ser mayor a 0"),
	),
});

export const ProductionRequestFormSchema = v.object({
	code: v.pipe(v.string(), v.minLength(1, "Código es requerido")),
	clientId: v.pipe(v.string(), v.minLength(1, "Cliente es requerido")),
	expectedDate: v.optional(v.string()),
	priority: v.pipe(v.string(), v.minLength(1, "Prioridad es requerida")),
	requests: v.pipe(
		v.array(ProductionRequestItemSchema),
		v.minLength(1, "Debe agregar al menos un producto"),
	),
});

export type ProductionRequestFormData = v.InferInput<typeof ProductionRequestFormSchema>;
export type ProductionRequestItemFormData = v.InferInput<typeof ProductionRequestItemSchema>;
