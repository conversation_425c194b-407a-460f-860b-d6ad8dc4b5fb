import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { Calendar, Code } from "lucide-react";
import { useEffect, useState } from "react";
import { useService } from "src/config/context/serviceProvider";
import { useAppForm } from "src/core/components/form/form";
import { clientOptions } from "src/modules/client/hooks/client-options";
import {
	type CreateProductionRequest,
	ProductionRequestState,
	type UpdateProductionRequest,
} from "../../service/model/production-request";
import { shoppingCartStore } from "../../store/shopping-cart";
import PriorityDropdown from "../PriorityDropdown";
import ProductList from "../ProductList";
import ShoppingCart from "../ShoppingCart";
import {
	type ProductionRequestFormData,
	ProductionRequestFormSchema,
} from "./schema";

interface ProductionRequestFormProps {
	onSubmit: (data: CreateProductionRequest | UpdateProductionRequest) => void;
	defaultValues?: Partial<ProductionRequestFormData & { id?: string }>;
	isLoading?: boolean;
	submitText?: string;
}

export default function ProductionRequestForm({
	onSubmit,
	defaultValues,
	isLoading = false,
	submitText = "Guardar",
}: ProductionRequestFormProps) {
	const service = useService();
	const cartState = useStore(shoppingCartStore);
	const [showCancelModal, setShowCancelModal] = useState(false);

	const { data: clients = [] } = useQuery(clientOptions(service));

	const form = useAppForm({
		defaultValues: defaultValues || {
			code: "",
			clientId: "",
			expectedDate: "",
			priority: "",
			requests: [],
		},
		validators: {
			onChange: ProductionRequestFormSchema,
		},
		onSubmit: ({ value }) => {
			const requests = cartState.items.map((item) => ({
				productId: item.productId,
				quantity: item.quantity,
			}));

			const formData = {
				code: value.code || "",
				clientId: value.clientId || "",
				expectedDate: new Date(value.expectedDate || "").toISOString(),
				priority: value.priority || "",
				state: ProductionRequestState.PENDING,
				requests,
			};

			onSubmit(formData);
		},
	});

	// Sync cart items with form requests field
	useEffect(() => {
		const requests = cartState.items.map((item) => ({
			productId: item.productId,
			quantity: item.quantity,
		}));
		form.setFieldValue("requests", requests);
	}, [cartState.items, form]);

	const clientSelectOptions = clients.map((client) => ({
		value: client.id,
		label:
			`${client.name} ${client.fatherName || ""} ${client.motherName || ""}`.trim(),
	}));

	const handleCancel = () => {
		if (cartState.items.length > 0 || form.state.isDirty) {
			setShowCancelModal(true);
		} else {
			// Navigate back or reset form
			form.reset();
		}
	};

	const confirmCancel = () => {
		form.reset();
		setShowCancelModal(false);
		// Navigate back if needed
	};

	return (
		<div className="space-y-6">
			{/* Form Section */}
			<div className="card bg-base-300 shadow-sm">
				<div className="card-body">
					<form
						onSubmit={(e) => {
							e.preventDefault();
							form.handleSubmit();
						}}
					>
						<form.AppForm>
							{/* Hidden field for requests validation */}
							<form.AppField
								name="requests"
								children={() => <input type="hidden" />}
							/>

							<div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
								<form.AppField
									name="code"
									children={({ FSTextField }) => (
										<FSTextField
											label="Código"
											placeholder="Ingrese el código"
											prefixComponent={<Code size={16} />}
										/>
									)}
								/>

								<form.AppField
									name="clientId"
									children={({ FSSelectField }) => (
										<FSSelectField
											label="Cliente"
											placeholder="Seleccione un cliente"
											options={clientSelectOptions}
										/>
									)}
								/>

								<form.AppField
									name="expectedDate"
									children={({ FSTextField }) => (
										<FSTextField
											label="Fecha Esperada"
											placeholder="YYYY-MM-DD"
											type="date"
											prefixComponent={<Calendar size={16} />}
										/>
									)}
								/>

								<form.AppField
									name="priority"
									children={({ state, handleChange }) => (
										<div className="form-control">
											<label className="label" htmlFor="priority-dropdown">
												<span className="label-text">Prioridad</span>
											</label>
											<div id="priority-dropdown">
												<PriorityDropdown
													value={state.value || ""}
													onChange={handleChange}
													error={
														typeof state.meta.errors?.[0] === "string"
															? state.meta.errors[0]
															: undefined
													}
												/>
											</div>
										</div>
									)}
								/>
							</div>
						</form.AppForm>
					</form>
				</div>
			</div>

			{/* Products and Cart Section */}
			<div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
				<ProductList />
				<ShoppingCart />
			</div>

			{/* Action Buttons */}
			<div className="flex justify-end gap-4">
				<button type="button" className="btn btn-ghost" onClick={handleCancel}>
					Cancelar
				</button>
				<button
					type="button"
					className="btn btn-primary"
					onClick={() => form.handleSubmit()}
					disabled={isLoading || cartState.items.length === 0}
				>
					{isLoading && <span className="loading loading-spinner loading-sm" />}
					{submitText}
				</button>
			</div>

			{/* Cancel Confirmation Modal */}
			<div className={`modal ${showCancelModal ? "modal-open" : ""}`}>
				<div className="modal-box">
					<h3 className="font-bold text-lg">Confirmar Cancelación</h3>
					<p className="py-4">
						¿Está seguro que desea cancelar? Se perderán todos los cambios
						realizados.
					</p>
					<div className="modal-action">
						<button
							type="button"
							className="btn"
							onClick={() => setShowCancelModal(false)}
						>
							No, continuar
						</button>
						<button
							type="button"
							className="btn btn-error"
							onClick={confirmCancel}
						>
							Sí, cancelar
						</button>
					</div>
				</div>
			</div>
		</div>
	);
}
