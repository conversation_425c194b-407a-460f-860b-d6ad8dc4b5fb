import { useStore } from "@tanstack/react-store";
import { Minus, Plus, Trash2 } from "lucide-react";
import {
	shoppingCartActions,
	shoppingCartStore,
} from "../../store/shopping-cart";

export default function ShoppingCart() {
	const cartState = useStore(shoppingCartStore);

	const handleQuantityChange = (productId: string, quantity: number) => {
		if (quantity <= 0) {
			shoppingCartActions.removeItem(productId);
		} else {
			shoppingCartActions.updateQuantity(productId, quantity);
		}
	};

	const getTotalItems = () => {
		return cartState.items.reduce((total, item) => total + item.quantity, 0);
	};

	const getTotalPrice = () => {
		return cartState.items.reduce(
			(total, item) => total + item.quantity * item.productPrice,
			0,
		);
	};

	if (cartState.items.length === 0) {
		return (
			<div className="card border bg-base-100 shadow-sm">
				<div className="card-body">
					<h3 className="card-title text-lg">Carrito de Compras</h3>
					<div className="py-8 text-center text-base-content/60">
						<p>No hay productos en el carrito</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="card border bg-base-100 shadow-sm">
			<div className="card-body">
				<div className="mb-4 flex items-center justify-between">
					<h3 className="card-title text-lg">Carrito de Compras</h3>
					<div className="badge badge-primary">
						{getTotalItems()} {getTotalItems() === 1 ? "producto" : "productos"}
					</div>
				</div>

				<div className="max-h-96 space-y-3 overflow-y-auto">
					{cartState.items.map((item) => (
						<div
							key={item.productId}
							className="flex items-center justify-between rounded-lg bg-base-200 p-3"
						>
							<div className="flex-1">
								<h4 className="font-medium text-sm">{item.productName}</h4>
								<p className="text-base-content/70 text-xs">
									Código: {item.productCode}
								</p>
								<p className="text-base-content/70 text-xs">
									Precio: ${item.productPrice.toFixed(2)}
								</p>
							</div>

							<div className="flex items-center gap-2">
								<div className="flex items-center gap-1">
									<button
										type="button"
										className="btn btn-xs btn-circle btn-outline"
										onClick={() =>
											shoppingCartActions.decreaseQuantity(item.productId)
										}
									>
										<Minus size={12} />
									</button>
									<input
										type="number"
										className="input input-xs input-bordered w-16 text-center"
										min="1"
										value={item.quantity}
										onChange={(e) =>
											handleQuantityChange(
												item.productId,
												Number(e.target.value) || 0,
											)
										}
									/>
									<button
										type="button"
										className="btn btn-xs btn-circle btn-outline"
										onClick={() =>
											shoppingCartActions.increaseQuantity(item.productId)
										}
									>
										<Plus size={12} />
									</button>
								</div>
								<button
									type="button"
									className="btn btn-xs btn-circle btn-error"
									onClick={() => shoppingCartActions.removeItem(item.productId)}
								>
									<Trash2 size={12} />
								</button>
							</div>
						</div>
					))}
				</div>

				<div className="divider my-4" />

				<div className="flex items-center justify-between font-medium">
					<span>Total:</span>
					<span className="text-lg">${getTotalPrice().toFixed(2)}</span>
				</div>
			</div>
		</div>
	);
}
