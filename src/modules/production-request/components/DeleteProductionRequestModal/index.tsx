import { toast } from "react-toastify";
import { cn } from "src/core/utils/classes";
import { getErrorResult } from "src/core/utils/effectErrors";
import useDeleteProductionRequest from "../../hooks/use-delete-production-request";
import type { ProductionRequest } from "../../service/model/production-request";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	productionRequest: ProductionRequest;
}

export default function DeleteProductionRequestModal({
	isOpen,
	setIsOpen,
	productionRequest,
}: Props) {
	const { mutate, isPending } = useDeleteProductionRequest();

	const handleDelete = () => {
		mutate(productionRequest.id, {
			onSuccess: () => {
				toast.success("Solicitud de producción eliminada");
				setIsOpen(false);
			},
			onError: (_error) => {
				console.log(_error);
				const { error } = getErrorResult(_error);
				toast.error(error.message);
			},
		});
	};

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<h3 className="font-bold text-lg">Eliminar solicitud de producción</h3>
				<p className="py-4">
					¿Estás seguro de que quieres eliminar esta solicitud de producción?
				</p>
				<p className="text-sm text-gray-600">
					Código: {productionRequest.code}
				</p>
				<p className="text-sm text-gray-600">
					Estado: {productionRequest.state}
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-ghost"
						onClick={() => setIsOpen(false)}
						disabled={isPending}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={handleDelete}
						disabled={isPending}
					>
						{isPending ? (
							<span className="loading loading-spinner loading-sm"></span>
						) : (
							"Eliminar"
						)}
					</button>
				</div>
			</div>
		</div>
	);
}
