import { useStore } from "@tanstack/react-store";
import { Search, X } from "lucide-react";
import { productFiltersActions, productFiltersStore } from "../../store/product-filters";
import { ProductViewType, productViewStore } from "../../store/product-view";
import type { Brand } from "src/modules/brand/service/model/brand";
import type { Category } from "src/modules/category/service/model/category";

interface ProductFiltersProps {
	categories: Category[];
	brands: Brand[];
}

export default function ProductFilters({ categories, brands }: ProductFiltersProps) {
	const filters = useStore(productFiltersStore);
	const currentView = useStore(productViewStore);

	const categoryOptions = categories.map((category) => ({
		value: category.id,
		label: category.name,
	}));

	const brandOptions = brands.map((brand) => ({
		value: brand.id,
		label: brand.name,
	}));

	const hasActiveFilters = 
		filters.globalSearch || 
		filters.categoryId || 
		filters.brandId || 
		filters.codeSearch;

	return (
		<div className="space-y-4">
			{/* Global Search */}
			<div className="form-control">
				<div className="input-group">
					<span className="bg-base-200">
						<Search size={16} />
					</span>
					<input
						type="text"
						placeholder="Buscar productos..."
						className="input input-bordered flex-1"
						value={filters.globalSearch}
						onChange={(e) => productFiltersActions.setGlobalSearch(e.target.value)}
					/>
					{filters.globalSearch && (
						<button
							type="button"
							className="btn btn-square btn-outline"
							onClick={() => productFiltersActions.setGlobalSearch("")}
						>
							<X size={16} />
						</button>
					)}
				</div>
			</div>

			{/* Filter Row */}
			<div className="flex flex-wrap gap-4">
				{/* Category Filter */}
				<div className="form-control min-w-48">
					<select
						className="select select-bordered"
						value={filters.categoryId}
						onChange={(e) => productFiltersActions.setCategoryId(e.target.value)}
					>
						<option value="">Todas las categorías</option>
						{categoryOptions.map((option) => (
							<option key={option.value} value={option.value}>
								{option.label}
							</option>
						))}
					</select>
				</div>

				{/* Brand Filter */}
				<div className="form-control min-w-48">
					<select
						className="select select-bordered"
						value={filters.brandId}
						onChange={(e) => productFiltersActions.setBrandId(e.target.value)}
					>
						<option value="">Todas las marcas</option>
						{brandOptions.map((option) => (
							<option key={option.value} value={option.value}>
								{option.label}
							</option>
						))}
					</select>
				</div>

				{/* Code Filter - Only show in table view */}
				{currentView === ProductViewType.TABLE && (
					<div className="form-control min-w-48">
						<div className="input-group">
							<span className="bg-base-200">
								<Search size={16} />
							</span>
							<input
								type="text"
								placeholder="Buscar por código..."
								className="input input-bordered flex-1"
								value={filters.codeSearch}
								onChange={(e) => productFiltersActions.setCodeSearch(e.target.value)}
							/>
							{filters.codeSearch && (
								<button
									type="button"
									className="btn btn-square btn-outline"
									onClick={() => productFiltersActions.setCodeSearch("")}
								>
									<X size={16} />
								</button>
							)}
						</div>
					</div>
				)}

				{/* Clear Filters Button */}
				{hasActiveFilters && (
					<div className="form-control">
						<button
							type="button"
							className="btn btn-outline"
							onClick={productFiltersActions.clearFilters}
						>
							<X size={16} />
							Limpiar filtros
						</button>
					</div>
				)}
			</div>
		</div>
	);
}
