import { useStore } from "@tanstack/react-store";
import { Grid, Table } from "lucide-react";
import { useEffect } from "react";
import { getErrorResult } from "src/core/utils/effectErrors";
import useFilteredProducts from "../../hooks/use-filtered-products";
import {
	ProductViewType,
	productViewActions,
	productViewStore,
} from "../../store/product-view";
import ProductCard from "../ProductCard";
import ProductFilters from "../ProductFilters";
import ProductTable from "../ProductTable";

export default function ProductList() {
	const currentView = useStore(productViewStore);

	const {
		products,
		categories,
		brands,
		isLoading: isPending,
		error,
	} = useFilteredProducts();

	const isError = !!error;

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError) {
		return (
			<div className="card border bg-base-100 shadow-sm">
				<div className="card-body">
					<div className="py-8 text-center text-error">
						Error: {getErrorResult(error).error.message}
					</div>
				</div>
			</div>
		);
	}

	if (isPending) {
		return (
			<div className="card border bg-base-100 shadow-sm">
				<div className="card-body">
					<div className="py-8 text-center">
						<span className="loading loading-lg loading-spinner" />
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="card border bg-base-100 shadow-sm">
			<div className="card-body">
				<div className="mb-4 flex items-center justify-between">
					<h3 className="card-title text-lg">Lista de Productos</h3>
					<div className="tabs tabs-boxed">
						<button
							type="button"
							className={`tab gap-2 ${
								currentView === ProductViewType.CARDS ? "tab-active" : ""
							}`}
							onClick={() => productViewActions.setView(ProductViewType.CARDS)}
						>
							<Grid size={16} />
							Tarjetas
						</button>
						<button
							type="button"
							className={`tab gap-2 ${
								currentView === ProductViewType.TABLE ? "tab-active" : ""
							}`}
							onClick={() => productViewActions.setView(ProductViewType.TABLE)}
						>
							<Table size={16} />
							Tabla
						</button>
					</div>
				</div>

				{/* Filters */}
				<div className="mb-6">
					<ProductFilters categories={categories} brands={brands} />
				</div>

				{currentView === ProductViewType.CARDS ? (
					<div className="grid max-h-96 grid-cols-1 gap-4 overflow-y-auto md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
						{products.map((product) => (
							<ProductCard key={product.id} product={product} />
						))}
					</div>
				) : (
					<div className="max-h-96 overflow-y-auto">
						<ProductTable products={products} />
					</div>
				)}

				{products.length === 0 && (
					<div className="py-8 text-center text-base-content/60">
						<p>No hay productos disponibles</p>
					</div>
				)}
			</div>
		</div>
	);
}
