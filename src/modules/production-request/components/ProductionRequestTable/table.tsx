import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "src/core/components/tables/BasicTable";
import type { ProductionRequest } from "../../service/model/production-request";
import { columns } from "./columns";

interface Props {
	productionRequests: ProductionRequest[];
}

export default function Table({ productionRequests }: Props) {
	const table = useReactTable({
		data: productionRequests,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
