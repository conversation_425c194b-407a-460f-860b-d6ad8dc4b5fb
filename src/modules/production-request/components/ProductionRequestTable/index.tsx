import { useQuery } from "@tanstack/react-query";
import { useService } from "~/config/context/serviceProvider";
import { productionRequestOptions } from "../../hooks/production-request-options";
import Table from "./table";

export default function ProductionRequestTable() {
	const service = useService();

	const {
		data: productionRequests,
		isLoading,
		error,
	} = useQuery(productionRequestOptions(service));

	if (isLoading) {
		return (
			<div className="flex items-center justify-center p-8">
				<span className="loading loading-spinner loading-lg" />
			</div>
		);
	}

	if (error) {
		return (
			<div className="alert alert-error">
				<span>Error al cargar las solicitudes de producción</span>
			</div>
		);
	}

	if (!productionRequests || productionRequests.length === 0) {
		return (
			<div className="p-8 text-center">
				<p className="text-gray-500">
					No hay solicitudes de producción registradas
				</p>
			</div>
		);
	}

	return <Table productionRequests={productionRequests} />;
}
