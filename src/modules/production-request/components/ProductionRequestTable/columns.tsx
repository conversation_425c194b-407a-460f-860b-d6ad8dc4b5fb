import { Link } from "@tanstack/react-router";
import { createColumnHelper } from "@tanstack/react-table";
import { Check, Edit, Trash, X } from "lucide-react";
import { useState } from "react";
import { toast } from "react-toastify";
import { getErrorResult } from "src/core/utils/effectErrors";
import useApproveProductionRequest from "../../hooks/use-approve-production-request";
import useRejectProductionRequest from "../../hooks/use-reject-production-request";
import type { ProductionRequest } from "../../service/model/production-request";
import { ProductionRequestState } from "../../service/model/production-request";
import DeleteProductionRequestModal from "../DeleteProductionRequestModal";

const columnHelper = createColumnHelper<ProductionRequest>();

const getStateColor = (state: string) => {
	switch (state) {
		case ProductionRequestState.PENDING:
			return "badge-warning";
		case ProductionRequestState.APPROVED:
			return "badge-success";
		case ProductionRequestState.REJECTED:
			return "badge-error";
		case ProductionRequestState.IN_PROGRESS:
			return "badge-info";
		case ProductionRequestState.COMPLETED:
			return "badge-success";
		default:
			return "badge-neutral";
	}
};

const getStateText = (state: string) => {
	switch (state) {
		case ProductionRequestState.PENDING:
			return "Pendiente";
		case ProductionRequestState.APPROVED:
			return "Aprobado";
		case ProductionRequestState.REJECTED:
			return "Rechazado";
		case ProductionRequestState.IN_PROGRESS:
			return "En Progreso";
		case ProductionRequestState.COMPLETED:
			return "Completado";
		default:
			return state;
	}
};

const getPriorityColor = (priority: string) => {
	switch (priority) {
		case "low":
			return "badge-neutral";
		case "medium":
			return "badge-warning";
		case "high":
			return "badge-error";
		case "urgent":
			return "badge-error";
		default:
			return "badge-neutral";
	}
};

const getPriorityText = (priority: string) => {
	switch (priority) {
		case "low":
			return "Baja";
		case "medium":
			return "Media";
		case "high":
			return "Alta";
		case "urgent":
			return "Urgente";
		default:
			return priority;
	}
};

export const columns = [
	columnHelper.accessor("code", {
		header: "Código",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("clientId", {
		header: "Cliente",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("expectedDate", {
		header: "Fecha Esperada",
		cell: (info) => {
			const date = info.getValue();
			return date ? new Date(date).toLocaleDateString() : "-";
		},
	}),
	columnHelper.accessor("priority", {
		header: "Prioridad",
		cell: (info) => {
			const priority = info.getValue();
			return (
				<span className={`badge ${getPriorityColor(priority)}`}>
					{getPriorityText(priority)}
				</span>
			);
		},
	}),
	columnHelper.accessor("state", {
		header: "Estado",
		cell: (info) => {
			const state = info.getValue();
			return (
				<span className={`badge ${getStateColor(state)}`}>
					{getStateText(state)}
				</span>
			);
		},
	}),
	columnHelper.accessor("requests", {
		header: "Productos",
		cell: (info) => {
			const requests = info.getValue();
			return <span>{requests.length} producto(s)</span>;
		},
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			const productionRequest = row.original;
			const approveMutation = useApproveProductionRequest();
			const rejectMutation = useRejectProductionRequest();

			const handleApprove = () => {
				approveMutation.mutate(productionRequest.id, {
					onSuccess: () => {
						toast.success("Solicitud de producción aprobada");
					},
					onError: (error) => {
						console.log(error);
						const { error: errorResult } = getErrorResult(error);
						toast.error(errorResult.message);
					},
				});
			};

			const handleReject = () => {
				rejectMutation.mutate(productionRequest.id, {
					onSuccess: () => {
						toast.success("Solicitud de producción rechazada");
					},
					onError: (error) => {
						console.log(error);
						const { error: errorResult } = getErrorResult(error);
						toast.error(errorResult.message);
					},
				});
			};

			const canApproveOrReject =
				productionRequest.state === ProductionRequestState.PENDING;

			return (
				<div className="flex gap-2">
					<Link
						to="/admin/manufacture/production-requests/edit/$id"
						params={{ id: productionRequest.id }}
						className="btn btn-sm btn-primary"
						title="Editar"
					>
						<Edit size={16} />
					</Link>
					{canApproveOrReject && (
						<>
							<button
								type="button"
								className="btn btn-sm btn-success"
								onClick={handleApprove}
								disabled={approveMutation.isPending}
								title="Aprobar"
							>
								<Check size={16} />
							</button>
							<button
								type="button"
								className="btn btn-sm btn-error"
								onClick={handleReject}
								disabled={rejectMutation.isPending}
								title="Rechazar"
							>
								<X size={16} />
							</button>
						</>
					)}
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsDeleteOpen(true)}
						title="Eliminar"
					>
						<Trash size={16} />
					</button>
					<DeleteProductionRequestModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						productionRequest={productionRequest}
					/>
				</div>
			);
		},
	}),
];
