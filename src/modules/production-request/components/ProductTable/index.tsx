import { createColumn<PERSON>elper, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { Plus } from "lucide-react";
import { useState } from "react";
import BasicTable from "src/core/components/tables/BasicTable";
import type { Product } from "src/modules/product/service/model/product";
import { shoppingCartActions } from "../../store/shopping-cart";

interface ProductTableProps {
	products: Product[];
}

const columnHelper = createColumnHelper<Product>();

export default function ProductTable({ products }: ProductTableProps) {
	const [quantities, setQuantities] = useState<Record<string, number>>({});

	const handleQuantityChange = (productId: string, quantity: number) => {
		setQuantities(prev => ({
			...prev,
			[productId]: quantity || 0,
		}));
	};

	const handleAddToCart = (product: Product) => {
		const quantity = quantities[product.id] || 0;
		if (quantity > 0) {
			shoppingCartActions.addItem({
				productId: product.id,
				productName: product.name,
				productCode: product.code,
				productPrice: product.costPrice || 0,
				quantity,
			});
			setQuantities(prev => ({
				...prev,
				[product.id]: 0,
			}));
		}
	};

	const columns = [
		columnHelper.accessor("code", {
			header: "Código",
			cell: (info) => info.getValue(),
		}),
		columnHelper.accessor("name", {
			header: "Nombre",
			cell: (info) => info.getValue(),
		}),
		columnHelper.accessor("categoryIDs", {
			header: "Categoría",
			cell: (info) => {
				const categories = info.getValue();
				return categories?.length > 0 ? "Categoría" : "Sin categoría";
			},
		}),
		columnHelper.accessor("costPrice", {
			header: "Precio",
			cell: (info) => {
				const value = info.getValue();
				return value ? `$${value.toFixed(2)}` : "N/A";
			},
		}),
		columnHelper.display({
			id: "quantity",
			header: "Cantidad",
			cell: ({ row }) => {
				const product = row.original;
				return (
					<input
						type="number"
						className="input input-sm input-bordered w-20"
						placeholder="0"
						min="0"
						value={quantities[product.id] || 0}
						onChange={(e) => handleQuantityChange(product.id, Number(e.target.value))}
					/>
				);
			},
		}),
		columnHelper.display({
			id: "actions",
			header: "Acciones",
			cell: ({ row }) => {
				const product = row.original;
				const quantity = quantities[product.id] || 0;
				return (
					<button
						type="button"
						className="btn btn-sm btn-primary"
						onClick={() => handleAddToCart(product)}
						disabled={quantity <= 0}
					>
						<Plus size={16} />
						Agregar
					</button>
				);
			},
		}),
	];

	const table = useReactTable({
		data: products,
		columns,
		getCoreRowModel: getCoreRowModel(),
	});

	return <BasicTable table={table} />;
}
