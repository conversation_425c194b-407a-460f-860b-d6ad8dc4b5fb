import { Plus } from "lucide-react";
import { useState } from "react";
import type { Product } from "src/modules/product/service/model/product";
import { shoppingCartActions } from "../../store/shopping-cart";

interface ProductCardProps {
	product: Product;
}

export default function ProductCard({ product }: ProductCardProps) {
	const [quantity, setQuantity] = useState(0);

	const handleAddToCart = () => {
		if (quantity > 0) {
			shoppingCartActions.addItem({
				productId: product.id,
				productName: product.name,
				productCode: product.code,
				productPrice: product.costPrice || 0,
				quantity,
			});
			setQuantity(0);
		}
	};

	return (
		<div className="card border bg-base-100 shadow-sm">
			<div className="card-body p-4">
				<h3 className="card-title font-medium text-sm">{product.name}</h3>
				<div className="space-y-2 text-sm">
					<p className="text-base-content/70">
						<span className="font-medium">Categoría:</span>{" "}
						{product.categoryIDs?.length > 0 ? "Categoría" : "Sin categoría"}
					</p>
					<p className="text-base-content/70">
						<span className="font-medium">Precio:</span>{" "}
						{product.costPrice ? `$${product.costPrice.toFixed(2)}` : "N/A"}
					</p>
				</div>
				<div className="card-actions mt-4 items-center justify-between">
					<div className="flex items-center gap-2">
						<input
							type="number"
							className="input input-sm input-bordered w-20"
							placeholder="Cant."
							min="0"
							value={quantity}
							onChange={(e) => setQuantity(Number(e.target.value) || 0)}
						/>
					</div>
					<button
						type="button"
						className="btn btn-sm btn-primary"
						onClick={handleAddToCart}
						disabled={quantity <= 0}
					>
						<Plus size={16} />
						Agregar
					</button>
				</div>
			</div>
		</div>
	);
}
