import {
	<PERSON>ert<PERSON>ircle,
	AlertTriangle,
	ChevronDown,
	Circle,
	Zap,
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { ProductionRequestPriority } from "../../service/model/production-request";

interface PriorityOption {
	value: ProductionRequestPriority;
	label: string;
	icon: React.ReactNode;
	color: string;
}

const priorityOptions: PriorityOption[] = [
	{
		value: ProductionRequestPriority.LOW,
		label: "Baja",
		icon: <Circle size={16} />,
		color: "text-success",
	},
	{
		value: ProductionRequestPriority.MEDIUM,
		label: "Media",
		icon: <AlertCircle size={16} />,
		color: "text-warning",
	},
	{
		value: ProductionRequestPriority.HIGH,
		label: "Alta",
		icon: <AlertTriangle size={16} />,
		color: "text-error",
	},
	{
		value: ProductionRequestPriority.URGENT,
		label: "Urgente",
		icon: <Zap size={16} />,
		color: "text-error",
	},
];

interface PriorityDropdownProps {
	value: string;
	onChange: (value: string) => void;
	placeholder?: string;
	error?: string;
}

export default function PriorityDropdown({
	value,
	onChange,
	placeholder = "Seleccione prioridad",
	error,
}: PriorityDropdownProps) {
	const [isOpen, setIsOpen] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);

	const selectedOption = priorityOptions.find(
		(option) => option.value === value,
	);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setIsOpen(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	const handleSelect = (option: PriorityOption) => {
		onChange(option.value);
		setIsOpen(false);
	};

	return (
		<div className="form-control w-full">
			<div
				className={`dropdown ${isOpen ? "dropdown-open" : ""}`}
				ref={dropdownRef}
			>
				<button
					type="button"
					className={`btn btn-outline w-full justify-between ${error ? "btn-error" : ""}`}
					onClick={() => setIsOpen(!isOpen)}
				>
					<div className="flex items-center gap-2">
						{selectedOption ? (
							<>
								<span className={selectedOption.color}>
									{selectedOption.icon}
								</span>
								<span>{selectedOption.label}</span>
							</>
						) : (
							<span className="text-base-content/60">{placeholder}</span>
						)}
					</div>
					<ChevronDown
						size={16}
						className={`transition-transform ${isOpen ? "rotate-180" : ""}`}
					/>
				</button>
				<ul className="dropdown-content menu z-[1] w-full rounded-box border bg-base-100 p-2 shadow-lg">
					{priorityOptions.map((option) => (
						<li key={option.value}>
							<button
								type="button"
								className={`flex items-center gap-2 ${
									value === option.value ? "active" : ""
								}`}
								onClick={() => handleSelect(option)}
							>
								<span className={option.color}>{option.icon}</span>
								<span>{option.label}</span>
							</button>
						</li>
					))}
				</ul>
			</div>
			{error && (
				<div className="label">
					<span className="label-text-alt text-error">{error}</span>
				</div>
			)}
		</div>
	);
}
