import { useQueries, useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import { getErrorResult } from "src/core/utils/effectErrors";
import { productOptionsById } from "src/modules/product/hooks/product-options";
import { productionRequestOptionsById } from "../../hooks/production-request-options";
import useUpdateProductionRequest from "../../hooks/use-update-production-request";
import type { UpdateProductionRequest } from "../../service/model/production-request";
import { shoppingCartActions } from "../../store/shopping-cart";
import ProductionRequestForm from "../ProductionRequestForm";

interface EditProductionRequestProps {
	id: string;
}

export default function EditProductionRequest({
	id,
}: EditProductionRequestProps) {
	const navigate = useNavigate();
	const service = useService();
	const updateProductionRequestMutation = useUpdateProductionRequest();

	const {
		data: productionRequest,
		isLoading,
		isError,
		error,
	} = useQuery({
		...productionRequestOptionsById(service, id),
		enabled: !!id,
	});

	// Fetch product details for each request item
	const productQueries = useQueries({
		queries: (productionRequest?.requests || []).map((request) => ({
			...productOptionsById(service, request.productId),
			enabled: !!productionRequest && !!request.productId,
		})),
	});

	const handleSubmit = (data: UpdateProductionRequest) => {
		updateProductionRequestMutation.mutate(
			{ ...data, id },
			{
				onSuccess: () => {
					shoppingCartActions.clearCart();
					navigate({ to: "/admin/manufacture/production-requests" });
				},
			},
		);
	};

	// Load existing items into cart when production request and products are loaded
	useEffect(() => {
		if (productionRequest?.requests && productQueries.length > 0) {
			// Check if all product queries have completed successfully
			const allProductsLoaded = productQueries.every(
				(query) => query.isSuccess && query.data,
			);

			if (allProductsLoaded) {
				shoppingCartActions.clearCart();

				// Populate cart with existing request items
				for (const request of productionRequest.requests) {
					const productQuery = productQueries.find(
						(query) => query.data?.id === request.productId,
					);

					if (productQuery?.data) {
						const product = productQuery.data;
						shoppingCartActions.addItem({
							productId: product.id,
							productName: product.name,
							productCode: product.code,
							productPrice: product.costPrice || 0,
							quantity: request.quantity,
						});
					}
				}
			}
		}
	}, [productionRequest, productQueries]);

	// Clear cart when component unmounts
	useEffect(() => {
		return () => {
			shoppingCartActions.clearCart();
		};
	}, []);

	// Check if we're still loading production request or products
	const isLoadingProducts = productQueries.some((query) => query.isLoading);
	const isLoadingData = isLoading || isLoadingProducts;

	// Check for product query errors
	const productQueryErrors = productQueries.filter((query) => query.isError);

	if (isError) {
		return (
			<div className="container mx-auto p-6">
				<div className="alert alert-error">
					<span>Error: {getErrorResult(error).error.message}</span>
				</div>
			</div>
		);
	}

	// Show error if any product queries failed
	if (productQueryErrors.length > 0) {
		return (
			<div className="container mx-auto p-6">
				<div className="alert alert-error">
					<span>Error loading product details. Please try again.</span>
				</div>
			</div>
		);
	}

	if (isLoadingData) {
		return (
			<div className="container mx-auto p-6">
				<div className="flex justify-center py-8">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	if (!productionRequest) {
		return (
			<div className="container mx-auto p-6">
				<div className="alert alert-warning">
					<span>Solicitud de producción no encontrada</span>
				</div>
			</div>
		);
	}

	const defaultValues = {
		code: productionRequest.code,
		clientId: productionRequest.clientId,
		expectedDate: productionRequest.expectedDate || "",
		priority: productionRequest.priority,
		requests: productionRequest.requests.map((item) => ({
			productId: item.productId,
			quantity: item.quantity,
		})),
	};

	return (
		<div className="container mx-auto p-6">
			<div className="mb-6">
				<h1 className="font-bold text-2xl">Editar Solicitud de Producción</h1>
				<p className="text-base-content/70">
					Modifique los datos de la solicitud de producción
				</p>
			</div>

			<ProductionRequestForm
				onSubmit={handleSubmit}
				defaultValues={defaultValues}
				isLoading={updateProductionRequestMutation.isPending}
				submitText="Actualizar Solicitud"
			/>
		</div>
	);
}
