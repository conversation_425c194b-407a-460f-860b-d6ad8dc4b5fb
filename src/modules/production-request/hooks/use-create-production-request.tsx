import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { toast } from "react-toastify";
import { useService } from "src/config/context/serviceProvider";
import { getErrorResult } from "src/core/utils/effectErrors";
import type {
	CreateProductionRequest,
	ProductionRequest,
} from "../service/model/production-request";
import { ProductionRequestRuntime } from "../service/runtime";
import { productionRequestOptions } from "./production-request-options";

export default function useCreateProductionRequest() {
	const service = useService();
	const { productionRequest } = service;
	const queryClient = useQueryClient();
	const queryKey = productionRequestOptions(service).queryKey;

	return useMutation({
		mutationKey: ["create-production-request"],
		mutationFn: (newProductionRequest: CreateProductionRequest) =>
			ProductionRequestRuntime.runPromise(
				productionRequest.create(newProductionRequest),
			),
		onMutate: async (newProductionRequest) => {
			await queryClient.cancelQueries({ queryKey });

			const previousProductionRequests = queryClient.getQueryData(queryKey);

			if (previousProductionRequests) {
				const optimisticProductionRequest: ProductionRequest = {
					id: `temp-${Date.now()}`,
					...newProductionRequest,
					expectedDate: newProductionRequest.expectedDate || null,
					requests: [],
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
				};

				queryClient.setQueryData(
					queryKey,
					create(previousProductionRequests, (draft) => {
						draft.unshift(optimisticProductionRequest);
					}),
				);
			}

			return { previousProductionRequests };
		},
		onError: (error, _, context) => {
			queryClient.setQueryData(queryKey, context?.previousProductionRequests);
			const errorResult = getErrorResult(error);
			toast.error(
				`Error al crear solicitud de producción: ${errorResult.error.message}`,
			);
		},
		onSuccess: () => {
			toast.success("Solicitud de producción creada exitosamente");
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
