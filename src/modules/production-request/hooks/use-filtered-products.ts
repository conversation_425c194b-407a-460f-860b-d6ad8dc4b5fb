import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { useMemo } from "react";
import { useService } from "src/config/context/serviceProvider";
import { brandOptions } from "src/modules/brand/hooks/brand-options";
import { categoryChildrenByParentCodeOptions } from "src/modules/category/hooks/category-options";
import { productOptionsByCategoryCode } from "src/modules/product/hooks/product-options";
import { CategoryCode } from "~/modules/category/service/model/category";
import { productFiltersStore } from "../store/product-filters";

export default function useFilteredProducts() {
	const service = useService();
	const filters = useStore(productFiltersStore);

	const {
		data: products = [],
		isLoading: isLoadingProducts,
		error: productsError,
	} = useQuery(productOptionsByCategoryCode(service, CategoryCode.PRODUCTS));

	const { data: categories = [] } = useQuery(
		categoryChildrenByParentCodeOptions(service, CategoryCode.PRODUCTS),
	);
	const { data: brands = [] } = useQuery(brandOptions(service));

	const filteredProducts = useMemo(() => {
		let filtered = [...products];

		// Global search filter
		if (filters.globalSearch.trim()) {
			const searchTerm = filters.globalSearch.toLowerCase().trim();
			filtered = filtered.filter(
				(product) =>
					product.name.toLowerCase().includes(searchTerm) ||
					product.code.toLowerCase().includes(searchTerm) ||
					product.description?.toLowerCase().includes(searchTerm),
			);
		}

		// Category filter
		if (filters.categoryId) {
			filtered = filtered.filter((product) =>
				product.categoryIDs?.includes(filters.categoryId),
			);
		}

		// Brand filter
		if (filters.brandId) {
			filtered = filtered.filter(
				(product) => product.brandID === filters.brandId,
			);
		}

		// Code search filter (for table view)
		if (filters.codeSearch.trim()) {
			const codeSearchTerm = filters.codeSearch.toLowerCase().trim();
			filtered = filtered.filter((product) =>
				product.code.toLowerCase().includes(codeSearchTerm),
			);
		}

		return filtered;
	}, [products, filters]);

	return {
		products: filteredProducts,
		allProducts: products,
		categories,
		brands,
		isLoading: isLoadingProducts,
		error: productsError,
		filters,
	};
}
