import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import type { ProductionRequest } from "../service/model/production-request";
import { ProductionRequestRuntime } from "../service/runtime";
import { productionRequestOptions } from "./production-request-options";

export default function useDeleteProductionRequest() {
	const service = useService();
	const { productionRequest } = service;
	const queryClient = useQueryClient();
	const queryKey = productionRequestOptions(service).queryKey;

	return useMutation({
		mutationKey: ["delete-production-request"],
		mutationFn: (id: string) =>
			ProductionRequestRuntime.runPromise(productionRequest.delete(id)),
		onMutate: async (id) => {
			await queryClient.cancelQueries({ queryKey });

			const previousProductionRequests = queryClient.getQueryData(queryKey);

			if (previousProductionRequests) {
				queryClient.setQueryData(
					queryKey,
					create(previousProductionRequests, (draft) => {
						const index = draft.findIndex(
							(pr: ProductionRequest) => pr.id === id,
						);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}),
				);
			}

			return { previousProductionRequests };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousProductionRequests);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
