import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { ProductionRequestRuntime } from "../service/runtime";

export const productionRequestOptions = ({
	productionRequest,
}: serviceRegistry) =>
	queryOptions({
		queryKey: ["production-requests"],
		queryFn: () =>
			ProductionRequestRuntime.runPromise(productionRequest.getAll()),
	});

export const productionRequestOptionsById = (
	{ productionRequest }: serviceRegistry,
	id: string,
) =>
	queryOptions({
		queryKey: ["production-requests", id],
		queryFn: () =>
			ProductionRequestRuntime.runPromise(productionRequest.getById(id)),
	});
