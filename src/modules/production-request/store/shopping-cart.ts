import { Store } from "@tanstack/react-store";

export interface CartItem {
	productId: string;
	productName: string;
	productCode: string;
	productPrice: number;
	quantity: number;
}

export interface ShoppingCartState {
	items: CartItem[];
}

export const shoppingCartStore = new Store<ShoppingCartState>({
	items: [],
});

export const shoppingCartActions = {
	addItem: (item: Omit<CartItem, "quantity"> & { quantity: number }) => {
		shoppingCartStore.setState((prev) => {
			const existingItemIndex = prev.items.findIndex(
				(cartItem) => cartItem.productId === item.productId,
			);

			if (existingItemIndex >= 0) {
				// Update existing item quantity
				const updatedItems = [...prev.items];
				updatedItems[existingItemIndex] = {
					...updatedItems[existingItemIndex],
					quantity: updatedItems[existingItemIndex].quantity + item.quantity,
				};
				return { ...prev, items: updatedItems };
			} else {
				// Add new item
				return {
					...prev,
					items: [...prev.items, item],
				};
			}
		});
	},

	updateQuantity: (productId: string, quantity: number) => {
		shoppingCartStore.setState((prev) => {
			if (quantity <= 0) {
				return {
					...prev,
					items: prev.items.filter((item) => item.productId !== productId),
				};
			}

			const updatedItems = prev.items.map((item) =>
				item.productId === productId ? { ...item, quantity } : item,
			);
			return { ...prev, items: updatedItems };
		});
	},

	removeItem: (productId: string) => {
		shoppingCartStore.setState((prev) => ({
			...prev,
			items: prev.items.filter((item) => item.productId !== productId),
		}));
	},

	clearCart: () => {
		shoppingCartStore.setState({ items: [] });
	},

	increaseQuantity: (productId: string) => {
		shoppingCartStore.setState((prev) => {
			const updatedItems = prev.items.map((item) =>
				item.productId === productId
					? { ...item, quantity: item.quantity + 1 }
					: item,
			);
			return { ...prev, items: updatedItems };
		});
	},

	decreaseQuantity: (productId: string) => {
		shoppingCartStore.setState((prev) => {
			const updatedItems = prev.items
				.map((item) =>
					item.productId === productId
						? { ...item, quantity: item.quantity - 1 }
						: item,
				)
				.filter((item) => item.quantity > 0);
			return { ...prev, items: updatedItems };
		});
	},
};
