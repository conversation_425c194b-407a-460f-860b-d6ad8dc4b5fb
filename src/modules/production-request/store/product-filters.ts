import { Store } from "@tanstack/react-store";

export interface ProductFilters {
	globalSearch: string;
	categoryId: string;
	brandId: string;
	codeSearch: string; // Only for table view
}

export const productFiltersStore = new Store<ProductFilters>({
	globalSearch: "",
	categoryId: "",
	brandId: "",
	codeSearch: "",
});

export const productFiltersActions = {
	setGlobalSearch: (search: string) => {
		productFiltersStore.setState((prev) => ({
			...prev,
			globalSearch: search,
		}));
	},

	setCategoryId: (categoryId: string) => {
		productFiltersStore.setState((prev) => ({
			...prev,
			categoryId,
		}));
	},

	setBrandId: (brandId: string) => {
		productFiltersStore.setState((prev) => ({
			...prev,
			brandId,
		}));
	},

	setCodeSearch: (codeSearch: string) => {
		productFiltersStore.setState((prev) => ({
			...prev,
			codeSearch,
		}));
	},

	clearFilters: () => {
		productFiltersStore.setState({
			globalSearch: "",
			categoryId: "",
			brandId: "",
			codeSearch: "",
		});
	},
};
