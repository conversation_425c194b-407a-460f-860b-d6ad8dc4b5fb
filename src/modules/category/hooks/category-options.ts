import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { CategoryCode } from "../service/model/category";

export const categoryOptions = ({ category }: serviceRegistry) =>
	queryOptions({
		queryKey: ["categories"],
		queryFn: () => AppRuntime.runPromise(category.getAll()),
	});

export const categoryOptionsById = (
	{ category }: serviceRegistry,
	id: string,
) =>
	queryOptions({
		queryKey: ["categories", id],
		queryFn: () => AppRuntime.runPromise(category.getById(id)),
	});

export const categorySubcategoriesOptions = (
	{ category }: serviceRegistry,
	id: string,
) =>
	queryOptions({
		queryKey: ["categories", id, "subcategories"],
		queryFn: () => AppRuntime.runPromise(category.getSubcategories(id)),
	});

export const categoryChildrenByParentCodeOptions = (
	{ category }: serviceRegistry,
	parentCode: CategoryCode,
) =>
	queryOptions({
		queryKey: ["categories", "parent", parentCode, "children"],
		queryFn: () =>
			AppRuntime.runPromise(
				category.getCategoryChildrenByParentCode(parentCode),
			),
	});

export const categoryDetailsOptions = (
	{ category }: serviceRegistry,
	id: string,
) =>
	queryOptions({
		queryKey: ["categories", id, "details"],
		queryFn: () => AppRuntime.runPromise(category.getDetails(id)),
	});

export const categoryParentsOptions = ({ category }: serviceRegistry) =>
	queryOptions({
		queryKey: ["categories", "parents"],
		queryFn: () => AppRuntime.runPromise(category.getParents()),
	});
