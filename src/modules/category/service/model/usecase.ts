import { Effect } from "effect";
import type { AppError } from "src/core/service/model/error";
import type {
	Category,
	CategoryCreate,
	CategoryUpdate,
	CategoryWithSubcategories,
} from "./category";

export class CategoryUsecase extends Effect.Tag("CategoryUsecase")<
	CategoryUsecase,
	{
		readonly getAll: () => Effect.Effect<Category[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Category, AppError>;
		readonly create: (
			category: CategoryCreate,
		) => Effect.Effect<string, AppError>;
		readonly update: (
			category: CategoryUpdate,
		) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly getSubcategories: (
			id: string,
		) => Effect.Effect<Category[], AppError>;
		readonly getDetails: (
			id: string,
		) => Effect.Effect<CategoryWithSubcategories, AppError>;
		readonly getParents: () => Effect.Effect<Category[], AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
		readonly getCategoryChildrenByParentCode: (
			parentCode: string,
		) => Effect.Effect<Category[], AppError>;
	}
>() {}
