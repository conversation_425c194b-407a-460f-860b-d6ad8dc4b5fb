import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { CategoryCode } from "~/modules/category/service/model/category";

export const productOptions = ({ product }: serviceRegistry) =>
	queryOptions({
		queryKey: ["products"],
		queryFn: () => AppRuntime.runPromise(product.getAll()),
	});

export const productOptionsById = ({ product }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["products", id],
		queryFn: () => AppRuntime.runPromise(product.getById(id)),
	});

export const productOptionsByCategoryCode = (
	{ product }: serviceRegistry,
	categoryCode: CategoryCode,
) =>
	queryOptions({
		queryKey: ["products", "category", categoryCode],
		queryFn: () =>
			AppRuntime.runPromise(product.getProductsByCategoryCode(categoryCode)),
	});
