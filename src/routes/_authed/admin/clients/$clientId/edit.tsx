import { useQuery } from "@tanstack/react-query";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import ClientForm from "src/modules/client/components/ClientForm";
import { clientOptionsById } from "src/modules/client/hooks/client-options";
import useUpdateClient from "src/modules/client/hooks/use-update-client";
import type { UpdateClient } from "src/modules/client/service/model/client";

export const Route = createFileRoute("/_authed/admin/clients/$clientId/edit")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Editar Cliente",
			},
		],
	}),
});

function RouteComponent() {
	const { clientId } = Route.useParams();
	const navigate = useNavigate();
	const service = useService();
	const updateClientMutation = useUpdateClient();

	const {
		data: client,
		isLoading,
		error,
	} = useQuery(clientOptionsById(service, clientId));

	const handleSubmit = (data: UpdateClient) => {
		updateClientMutation.mutate(
			{ ...data, id: clientId },
			{
				onSuccess: () => {
					navigate({ to: "/admin/clients/clients" });
				},
			},
		);
	};

	const handleBack = () => {
		navigate({ to: "/admin/clients/clients" });
	};

	if (isLoading) {
		return (
			<div className="flex h-64 items-center justify-center">
				<span className="loading loading-spinner loading-lg" />
			</div>
		);
	}

	if (error || !client) {
		return (
			<div className="alert alert-error">
				<span>Error al cargar el cliente</span>
			</div>
		);
	}

	const defaultValues = {
		id: client.id,
		name: client.name,
		fatherName: client.fatherName || "",
		motherName: client.motherName || "",
		clientType: client.clientType,
		documentType: client.documentType,
		document: client.document,
		ubication: client.ubication || "",
		socialReason: client.socialReason || "",
		commercialName: client.commercialName || "",
		condition: client.condition || "",
		state: client.state || "",
		hasRetentionRegime: client.hasRetentionRegime || false,
		businessLineId: client.businessLineID || "",
		subBusinessLineId: client.subBusinessLineID || "",
		channelId: client.channelID || "",
		sellerId: client.sellerID || "",
		contactName: client.contactName || "",
		email: client.email || "",
		phone: client.phone || "",
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center gap-4">
				<button
					type="button"
					onClick={handleBack}
					className="btn btn-ghost btn-sm"
				>
					<ArrowLeft size={16} />
					Volver
				</button>
				<h1 className="text-2xl font-bold">Editar Cliente</h1>
			</div>
			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<ClientForm
						onSubmit={handleSubmit}
						defaultValues={defaultValues}
						isLoading={updateClientMutation.isPending}
						submitText="Actualizar Cliente"
						isEdit={true}
					/>
				</div>
			</div>
		</div>
	);
}
