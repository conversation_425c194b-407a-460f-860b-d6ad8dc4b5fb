import { Link, createFileRoute } from "@tanstack/react-router";
import { Plus } from "lucide-react";
import ProductionRequestTable from "~/modules/production-request/components/ProductionRequestTable";

export const Route = createFileRoute(
	"/_authed/admin/manufacture/production-requests/",
)({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Solicitudes de Producción",
			},
		],
	}),
});

function RouteComponent() {
	return (
		<div className="container mx-auto space-y-6">
			<div className="flex items-center justify-between">
				<h1 className="font-bold text-2xl">Solicitudes de Producción</h1>
				<Link
					to="/admin/manufacture/production-requests/create"
					className="btn btn-primary"
				>
					<Plus size={16} />
					Nueva Solicitud
				</Link>
			</div>

			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<ProductionRequestTable />
				</div>
			</div>
		</div>
	);
}
